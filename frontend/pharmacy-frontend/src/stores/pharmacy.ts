import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Pharmacy } from '@/types/api'
import request from '@/utils/request'

export const usePharmacyStore = defineStore('pharmacy', () => {
  // 状态
  const currentPharmacy = ref<Pharmacy | null>(null)
  const userPharmacies = ref<Pharmacy[]>([])
  const allPharmacies = ref<Pharmacy[]>([])

  // 计算属性
  const hasPharmacies = computed(() => userPharmacies.value.length > 0)
  const currentPharmacyId = computed(() => currentPharmacy.value?.id || null)

  // 初始化：从localStorage恢复当前药店
  const initializePharmacy = () => {
    const storedPharmacy = localStorage.getItem('current_pharmacy')
    if (storedPharmacy) {
      try {
        currentPharmacy.value = JSON.parse(storedPharmacy)
      } catch (error) {
        console.error('Failed to parse stored pharmacy data:', error)
        localStorage.removeItem('current_pharmacy')
      }
    }
  }

  // 获取用户关联的药店列表
  const fetchUserPharmacies = async (): Promise<void> => {
    try {
      const pharmacies = await request.get<Pharmacy[]>('/pharmacies/user')
      userPharmacies.value = pharmacies
      
      // 如果没有当前药店且有可用药店，设置第一个为当前药店
      if (!currentPharmacy.value && pharmacies.length > 0) {
        setCurrentPharmacy(pharmacies[0])
      }
    } catch (error) {
      console.error('Failed to fetch user pharmacies:', error)
      throw error
    }
  }

  // 获取所有药店列表（管理员用）
  const fetchAllPharmacies = async (): Promise<void> => {
    try {
      const pharmacies = await request.get<Pharmacy[]>('/pharmacies')
      allPharmacies.value = pharmacies
    } catch (error) {
      console.error('Failed to fetch all pharmacies:', error)
      throw error
    }
  }

  // 设置当前药店
  const setCurrentPharmacy = (pharmacy: Pharmacy) => {
    currentPharmacy.value = pharmacy
    localStorage.setItem('current_pharmacy', JSON.stringify(pharmacy))
  }

  // 切换药店
  const switchPharmacy = async (pharmacyId: number): Promise<void> => {
    try {
      // 调用后端切换药店接口
      await request.post('/pharmacies/switch', { pharmacyId })
      
      // 从用户药店列表中找到对应药店
      const pharmacy = userPharmacies.value.find(p => p.id === pharmacyId)
      if (pharmacy) {
        setCurrentPharmacy(pharmacy)
      } else {
        throw new Error('药店不存在或无权限访问')
      }
    } catch (error) {
      console.error('Failed to switch pharmacy:', error)
      throw error
    }
  }

  // 创建药店
  const createPharmacy = async (pharmacyData: Omit<Pharmacy, 'id' | 'createdAt' | 'updatedAt'>): Promise<Pharmacy> => {
    try {
      const newPharmacy = await request.post<Pharmacy>('/pharmacies', pharmacyData)
      
      // 更新药店列表
      userPharmacies.value.push(newPharmacy)
      allPharmacies.value.push(newPharmacy)
      
      return newPharmacy
    } catch (error) {
      console.error('Failed to create pharmacy:', error)
      throw error
    }
  }

  // 更新药店
  const updatePharmacy = async (id: number, pharmacyData: Partial<Pharmacy>): Promise<Pharmacy> => {
    try {
      const updatedPharmacy = await request.put<Pharmacy>(`/pharmacies/${id}`, pharmacyData)
      
      // 更新本地状态
      const updatePharmacyInList = (list: Pharmacy[]) => {
        const index = list.findIndex(p => p.id === id)
        if (index !== -1) {
          list[index] = updatedPharmacy
        }
      }
      
      updatePharmacyInList(userPharmacies.value)
      updatePharmacyInList(allPharmacies.value)
      
      // 如果更新的是当前药店，也要更新当前药店状态
      if (currentPharmacy.value?.id === id) {
        setCurrentPharmacy(updatedPharmacy)
      }
      
      return updatedPharmacy
    } catch (error) {
      console.error('Failed to update pharmacy:', error)
      throw error
    }
  }

  // 删除药店
  const deletePharmacy = async (id: number): Promise<void> => {
    try {
      await request.delete(`/pharmacies/${id}`)
      
      // 从本地状态中移除
      userPharmacies.value = userPharmacies.value.filter(p => p.id !== id)
      allPharmacies.value = allPharmacies.value.filter(p => p.id !== id)
      
      // 如果删除的是当前药店，清除当前药店状态
      if (currentPharmacy.value?.id === id) {
        currentPharmacy.value = null
        localStorage.removeItem('current_pharmacy')
        
        // 如果还有其他药店，设置第一个为当前药店
        if (userPharmacies.value.length > 0) {
          setCurrentPharmacy(userPharmacies.value[0])
        }
      }
    } catch (error) {
      console.error('Failed to delete pharmacy:', error)
      throw error
    }
  }

  // 清除药店状态
  const clearPharmacyState = () => {
    currentPharmacy.value = null
    userPharmacies.value = []
    allPharmacies.value = []
    localStorage.removeItem('current_pharmacy')
  }

  return {
    // 状态
    currentPharmacy,
    userPharmacies,
    allPharmacies,
    hasPharmacies,
    currentPharmacyId,
    
    // 方法
    initializePharmacy,
    fetchUserPharmacies,
    fetchAllPharmacies,
    setCurrentPharmacy,
    switchPharmacy,
    createPharmacy,
    updatePharmacy,
    deletePharmacy,
    clearPharmacyState
  }
})
