import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/pharmacy/select',
    name: 'PharmacySelect',
    component: () => import('@/views/pharmacy/PharmacySelectView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: false }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/DashboardView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/pharmacies',
    name: 'Pharmacies',
    component: () => import('@/views/pharmacy/PharmacyListView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/patients',
    name: 'Patients',
    component: () => import('@/views/patient/PatientListView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/patients/:id',
    name: 'PatientDetail',
    component: () => import('@/views/patient/PatientDetailView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/medical-records',
    name: 'MedicalRecords',
    component: () => import('@/views/medical/MedicalRecordListView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/medical-records/new',
    name: 'NewMedicalRecord',
    component: () => import('@/views/medical/MedicalRecordFormView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/medical-records/:id',
    name: 'MedicalRecordDetail',
    component: () => import('@/views/medical/MedicalRecordDetailView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/quick-medicines',
    name: 'QuickMedicines',
    component: () => import('@/views/medicine/QuickMedicineListView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/statistics',
    name: 'Statistics',
    component: () => import('@/views/StatisticsView.vue'),
    meta: { requiresAuth: true, requiresPharmacySelection: true }
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/views/admin/AdminView.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFoundView.vue')
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  const pharmacyStore = usePharmacyStore()

  // 检查是否需要认证
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
    return
  }

  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && userStore.user?.role !== 'ADMIN') {
    next('/dashboard')
    return
  }

  // 如果已登录且访问登录页，重定向到药店选择页面或首页
  if (to.name === 'Login' && userStore.isLoggedIn) {
    if (!pharmacyStore.currentPharmacy) {
      next('/pharmacy/select')
    } else {
      next('/dashboard')
    }
    return
  }

  // 检查是否需要药店选择
  if (to.meta.requiresPharmacySelection && userStore.isLoggedIn) {
    // 如果没有当前药店，重定向到药店选择页面
    if (!pharmacyStore.currentPharmacy) {
      // 如果当前就在药店选择页面，不要重定向
      if (to.name !== 'PharmacySelect') {
        next('/pharmacy/select')
        return
      }
    }
  }

  next()
})

export default router
