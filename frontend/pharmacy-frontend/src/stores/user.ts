import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginRequest, LoginResponse } from '@/types/api'
import request from '@/utils/request'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const accessToken = ref<string>('')
  const refreshToken = ref<string>('')
  const isLoggedIn = computed(() => !!accessToken.value && !!user.value)

  // 初始化：从localStorage恢复状态
  const initializeAuth = () => {
    const storedAccessToken = localStorage.getItem('access_token')
    const storedRefreshToken = localStorage.getItem('refresh_token')
    const storedUser = localStorage.getItem('user')

    if (storedAccessToken && storedRefreshToken && storedUser) {
      accessToken.value = storedAccessToken
      refreshToken.value = storedRefreshToken
      try {
        user.value = JSON.parse(storedUser)
      } catch (error) {
        console.error('Failed to parse stored user data:', error)
        clearAuth()
      }
    }
  }

  // 登录
  const login = async (loginData: LoginRequest): Promise<void> => {
    try {
      const response = await request.post<LoginResponse>('/auth/login', loginData)
      
      // 保存认证信息
      accessToken.value = response.access_token
      refreshToken.value = response.refresh_token
      user.value = response.user

      // 持久化到localStorage
      localStorage.setItem('access_token', response.access_token)
      localStorage.setItem('refresh_token', response.refresh_token)
      localStorage.setItem('user', JSON.stringify(response.user))
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    try {
      // 调用后端登出接口
      await request.post('/auth/logout')
    } catch (error) {
      console.error('Logout request failed:', error)
    } finally {
      // 无论后端请求是否成功，都清除本地状态
      clearAuth()
    }
  }

  // 清除认证信息
  const clearAuth = () => {
    user.value = null
    accessToken.value = ''
    refreshToken.value = ''
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user')
  }

  // 获取用户信息
  const fetchUserInfo = async (): Promise<void> => {
    try {
      const userInfo = await request.get<User>('/auth/user')
      user.value = userInfo
      localStorage.setItem('user', JSON.stringify(userInfo))
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      clearAuth()
      throw error
    }
  }

  // 刷新Token
  const refreshAccessToken = async (): Promise<void> => {
    try {
      const response = await request.post<{ access_token: string }>('/auth/refresh', {
        refresh_token: refreshToken.value
      })
      
      accessToken.value = response.access_token
      localStorage.setItem('access_token', response.access_token)
    } catch (error) {
      console.error('Token refresh failed:', error)
      clearAuth()
      throw error
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string): Promise<void> => {
    try {
      await request.post('/auth/change-password', {
        oldPassword,
        newPassword
      })
    } catch (error) {
      console.error('Password change failed:', error)
      throw error
    }
  }

  return {
    // 状态
    user,
    accessToken,
    refreshToken,
    isLoggedIn,
    
    // 方法
    initializeAuth,
    login,
    logout,
    clearAuth,
    fetchUserInfo,
    refreshAccessToken,
    changePassword
  }
})
